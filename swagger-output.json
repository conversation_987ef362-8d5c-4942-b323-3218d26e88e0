{"openapi": "3.0.0", "info": {"title": "Sistema de Gerenciamento de RH - API", "description": "API completa para gerenciamento do departamento de Recursos Humanos, incluindo cadastro de funcionários, aplicação de aumentos salariais e geração de folhas de pagamento.", "version": "1.0.0", "contact": {"name": "DevMinds Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:5000/"}], "tags": [{"name": "Autenticação", "description": "Endpoints para autenticação de usuários"}, {"name": "Funcionários", "description": "Gerenciamento de funcionários"}, {"name": "Aumento Salarial", "description": "Aplicação e histórico de aumentos salariais"}, {"name": "Cargos", "description": "Gerenciamento de cargos"}, {"name": "Folha <PERSON>", "description": "Geração e consulta de folhas de pagamento"}], "paths": {"/auth/login": {"post": {"tags": ["Autenticação"], "summary": "Autenticar usuá<PERSON>", "description": "Autentica um usuário do RH e retorna um token JWT", "responses": {"200": {"description": "Autenticação realizada com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "Autenticado com sucesso!"}, "token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}}}}}, "401": {"description": "Credenciais inválidas", "content": {"application/json": {"schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> ou senha inválid<PERSON>!"}}}}}}, "500": {"description": "Erro interno do servidor"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "senha": {"type": "string", "example": "123"}}, "required": ["email", "<PERSON><PERSON>a"]}}}}}}, "/funcionarios/": {"post": {"tags": ["Funcionários"], "summary": "Cadastrar funcionário", "description": "Cadastra um novo funcionário no sistema", "responses": {"201": {"description": "Funcionário cadastrado com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "Funcionário cadastrado com sucesso!"}}}}}}, "400": {"description": "<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "Todos os campos são obrigatórios!"}}}}}}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"cpf": {"type": "string", "example": "12345678901"}, "nome": {"type": "string", "example": "<PERSON>"}, "salario": {"type": "number", "example": 5000}, "dataAdmissao": {"type": "string", "format": "date", "example": "2024-01-15"}, "cargoId": {"type": "integer", "example": 1}}, "required": ["cpf", "nome", "salario", "dataAdmissao", "cargoId"]}}}}}, "get": {"tags": ["Funcionários"], "summary": "Listar funcionários ativos", "description": "Lista todos os funcionários ativos (não demitidos)", "responses": {"200": {"description": "Lista de funcionários", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"fun_id": {"type": "integer", "example": 1}, "fun_cpf": {"type": "string", "example": "12345678901"}, "fun_nome": {"type": "string", "example": "<PERSON>"}, "fun_salario": {"type": "number", "example": 5000}, "fun_datadmissao": {"type": "string", "format": "date", "example": "2024-01-15"}, "fun_datademissao": {"type": "string", "format": "date", "nullable": true}, "car_id": {"type": "integer", "example": 1}}}}}}}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}]}}, "/funcionarios/{id}": {"get": {"tags": ["Funcionários"], "summary": "Buscar funcionário por ID", "description": "Busca um funcionário específico pelo ID", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Funcionário encontrado", "content": {"application/json": {"schema": {"type": "object", "properties": {"fun_id": {"type": "integer", "example": 1}, "fun_cpf": {"type": "string", "example": "12345678901"}, "fun_nome": {"type": "string", "example": "<PERSON>"}, "fun_salario": {"type": "number", "example": 5000}, "fun_datadmissao": {"type": "string", "format": "date", "example": "2024-01-15"}, "fun_datademissao": {"type": "string", "format": "date", "nullable": true}, "car_id": {"type": "integer", "example": 1}}}}}}, "400": {"description": "Bad Request"}, "404": {"description": "Funcionário não encontrado"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Funcionários"], "summary": "<PERSON><PERSON><PERSON>", "description": "Realiza a demissão lógica de um funcionário (atualiza data de demissão)", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Funcionário demitido com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "Funcionário demitido com sucesso!"}}}}}}, "400": {"description": "Bad Request"}, "404": {"description": "Funcionário não encontrado"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}]}}, "/aumentoSalario/aplicar": {"post": {"tags": ["Aumento Salarial"], "summary": "Aplicar aumento salarial", "description": "Aplica aumento salarial a todos os funcionários ativos por percentual", "responses": {"200": {"description": "Aumento aplicado com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "Aumento salarial aplicado com sucesso!"}, "percentual": {"type": "number", "example": 10.5}, "data": {"type": "string", "format": "date", "example": "2024-01-15"}, "usuario": {"type": "string", "example": "Maria do RH"}}}}}}, "400": {"description": "<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "Percentual é obrigatório e deve ser maior que 0!"}}}}}}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"percentual": {"type": "number", "example": 10.5, "description": "Percentual de aumento (ex: 10.5 para 10.5%)"}}, "required": ["percentual"]}}}}}}, "/aumentoSalario/historico": {"get": {"tags": ["Aumento Salarial"], "summary": "Listar histó<PERSON>", "description": "Lista o histórico de aumentos salariais aplicados", "responses": {"200": {"description": "Histórico de aumentos", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"aus_id": {"type": "integer", "example": 1}, "aus_data": {"type": "string", "format": "date", "example": "2024-01-15"}, "aus_percentual": {"type": "number", "example": 10.5}, "usu_id": {"type": "integer", "example": 1}}}}}}}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}]}}, "/cargos/": {"get": {"tags": ["Cargos"], "summary": "Listar cargos", "description": "Lista todos os cargos disponíveis", "responses": {"200": {"description": "Lista de cargos", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"car_id": {"type": "integer", "example": 1}, "car_descricao": {"type": "string", "example": "<PERSON><PERSON><PERSON> de Si<PERSON>mas"}}}}}}}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}]}}, "/folhaPagamento/gerar": {"post": {"tags": ["Folha <PERSON>"], "summary": "<PERSON><PERSON><PERSON> folha de paga<PERSON>", "description": "Gera uma nova folha de pagamento para um mês/ano específico", "responses": {"200": {"description": "Folha gerada com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "Folha gerada com sucesso!"}}}}}}, "400": {"description": "<PERSON><PERSON>"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"mes": {"type": "integer", "example": 1, "minimum": 1, "maximum": 12}, "ano": {"type": "integer", "example": 2024}}, "required": ["mes", "ano"]}}}}}}, "/folhaPagamento/": {"get": {"tags": ["Folha <PERSON>"], "summary": "Listar folhas de pagamento", "description": "<PERSON><PERSON> to<PERSON> as folhas de pagamento geradas", "responses": {"200": {"description": "Lista de folhas de pagamento", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"fol_id": {"type": "integer", "example": 1}, "fol_ano": {"type": "integer", "example": 2024}, "fol_mes": {"type": "integer", "example": 1}, "fol_valortotal": {"type": "number", "example": 15000}}}}}}}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}]}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Token JWT obtido através do endpoint de autenticação"}}, "schemas": {"Error": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "properties": {"type": "object", "properties": {"msg": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "description": {"type": "string", "example": "Mensagem de erro"}}}, "error": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "description": {"type": "string", "example": "Detalhes técnicos do erro"}}}}}}}}}, "security": [{"bearerAuth": []}]}