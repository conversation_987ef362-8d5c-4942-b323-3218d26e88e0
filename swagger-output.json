{"openapi": "3.0.0", "info": {"title": "Sistema de Gerenciamento de RH - API", "description": "API completa para gerenciamento do departamento de Recursos Humanos, incluindo cadastro de funcionários, aplicação de aumentos salariais e geração de folhas de pagamento.", "version": "1.0.0", "contact": {"name": "DevMinds Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:5000/"}], "tags": [{"name": "Autenticação", "description": "Endpoints para autenticação de usuários"}, {"name": "Funcionários", "description": "Gerenciamento de funcionários"}, {"name": "Aumento Salarial", "description": "Aplicação e histórico de aumentos salariais"}, {"name": "Cargos", "description": "Gerenciamento de cargos"}, {"name": "Folha <PERSON>", "description": "Geração e consulta de folhas de pagamento"}], "paths": {"/auth/login": {"post": {"description": "", "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"example": "any"}, "senha": {"example": "any"}}}}}}}}, "/funcionarios/": {"post": {"description": "", "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"cpf": {"example": "any"}, "nome": {"example": "any"}, "salario": {"example": "any"}, "dataAdmissao": {"example": "any"}, "cargoId": {"example": "any"}}}}}}}, "get": {"description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/funcionarios/{id}": {"get": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "delete": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/aumentoSalario/aplicar": {"post": {"description": "", "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"percentual": {"example": "any"}}}}}}}}, "/aumentoSalario/historico": {"get": {"description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/cargos/": {"get": {"description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/folhaPagamento/gerar": {"post": {"description": "", "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"mes": {"example": "any"}, "ano": {"example": "any"}}}}}}}}, "/folhaPagamento/": {"get": {"description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Token JWT obtido através do endpoint de autenticação"}}}, "security": [{"bearerAuth": []}]}