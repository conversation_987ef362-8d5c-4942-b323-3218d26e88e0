import swaggerAutogen from "swagger-autogen";
const doc = {
    info: {
        title: "Sistema de Gerenciamento de RH - API",
        description: "API completa para gerenciamento do departamento de Recursos Humanos, incluindo cadastro de funcionários, aplicação de aumentos salariais e geração de folhas de pagamento.",
        version: "1.0.0",
        contact: {
            name: "DevMinds Team",
            email: "<EMAIL>"
        }
    },
    host: 'localhost:5000',
    basePath: '/',
    schemes: ['http'],
    consumes: ['application/json'],
    produces: ['application/json'],
    tags: [
        {
            name: 'Autenticação',
            description: 'Endpoints para autenticação de usuários'
        },
        {
            name: 'Funcioná<PERSON>s',
            description: 'Gerenciamento de funcionários'
        },
        {
            name: 'Aumento Salarial',
            description: 'Aplicação e histórico de aumentos salariais'
        },
        {
            name: 'Cargos',
            description: 'Gerenciamento de cargos'
        },
        {
            name: '<PERSON><PERSON><PERSON> Pagamento',
            description: 'Geração e consulta de folhas de pagamento'
        }
    ],
    components: {
        securitySchemes: {
            bearerAuth: {
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT',
                description: 'Token JWT obtido através do endpoint de autenticação'
            }
        },
        schemas: {
            Error: {
                type: 'object',
                properties: {
                    msg: {
                        type: 'string',
                        description: 'Mensagem de erro'
                    },
                    error: {
                        type: 'string',
                        description: 'Detalhes técnicos do erro'
                    }
                }
            }
        }
    },
    security: [
        {
            bearerAuth: []
        }
    ]
}

const outputJson = "./swagger-output.json";
const routes = ['./server.js']

swaggerAutogen({openapi: '3.0.0'})(outputJson, routes, doc)
.then( async () => {
    await import('./server.js');
})